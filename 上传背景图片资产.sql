-- 上传背景图片资产到系统
-- 将本地图片文件注册为系统资产，获取资产ID用于组态

-- 清理旧的背景图片资产
DELETE FROM zutai_assets WHERE name LIKE '%control-room%' OR name LIKE '%control-panel%' OR name LIKE '%monitor-desk%';

-- 获取当前项目和租户信息
DO $$
DECLARE
    current_project_id TEXT;
    current_tenant_id TEXT;
    control_room_asset_id TEXT;
    control_panel_asset_id TEXT;
    monitor_desk_asset_id TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL
    LIMIT 1;

    -- 如果没有找到，使用默认值
    IF current_project_id IS NULL THEN
        current_project_id := 'guazhou_project';
    END IF;

    IF current_tenant_id IS NULL THEN
        current_tenant_id := 'guazhou_tenant';
    END IF;

    -- 生成唯一的资产ID
    control_room_asset_id := 'bg_control_room_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    control_panel_asset_id := 'bg_control_panel_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    monitor_desk_asset_id := 'bg_monitor_desk_' || EXTRACT(EPOCH FROM NOW())::BIGINT;

    -- 插入主控制室背景图片资产
    INSERT INTO zutai_assets (
        id,
        name,
        type,
        url,
        size,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        control_room_asset_id,
        'control-room-main.jpg',
        'image',
        '/9j/4AAQSkZJRgABAgEASABIAAD/4gIoSUNDX1BST0ZJTEUAAQEAAAIYYXBwbAQAAABtbnRyUkdCIFhZWiAH5gABAAEAAAAAAABhY3NwQVBQTAAAAABBUFBMAAAAAAAAAAAAAAAAAAAAAAAA9tYAAQAAAADTLWFwcGzs/aOOOIVHw220vU962hgvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAApkZXNjAAAA/AAAADBjcHJ0AAABLAAAAFB3dHB0AAABfAAAABRyWFlaAAABkAAAABRnWFlaAAABpAAAABRiWFlaAAABuAAAABRyVFJDAAABzAAAACBjaGFkAAAB7AAAACxiVFJDAAABzAAAACBnVFJDAAABzAAAACBtbHVjAAAAAAAAAAEAAAAMZW5VUwAAABQAAAAcAEQAaQBzAHAAbABhAHkAIABQADNtbHVjAAAAAAAAAAEAAAAMZW5VUwAAADQAAAAcAEMAbwBwAHkAcgBpAGcAaAB0ACAAQQBwAHAAbABlACAASQBuAGMALgAsACAAMgAwADIAMlhZWiAAAAAAAAD21QABAAAAANMsWFlaIAAAAAAAAIPfAAA9v////7tYWVogAAAAAAAASr8AALE3AAAKuVhZWiAAAAAAAAAoOAAAEQsAAMi5cGFyYQAAAAAAAwAAAAJmZgAA8qcAAA1ZAAAT0AAACltzZjMyAAAAAAABDEIAAAXe///zJgAAB5MAAP2Q///7ov///aMAAAPcAADAbv/bAEMABAIDAwMCBAMDAwQEBAQFCQYFBQUFCwgIBgkNCw0NDQsMDA4QFBEODxMPDAwSGBITFRYXFxcOERkbGRYaFBYXFv/bAEMBBAQEBQUFCgYGChYPDA8WFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFv/CABEIBDgHgAMBIgACEQEDEQH/xAAcAAABBQEBAQAAAAAAAAAAAAABAAIDBAUGBwj/',
        338530,
        EXTRACT(EPOCH FROM NOW()) * 1000,
        current_project_id,
        current_tenant_id
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        url = EXCLUDED.url,
        size = EXCLUDED.size;

    -- 插入控制面板背景图片资产
    INSERT INTO zutai_assets (
        id,
        name,
        type,
        url,
        size,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        control_panel_asset_id,
        'control-panel.jpg',
        'image',
        '/assets/images/backgrounds/control-panel.jpg',
        22079,
        EXTRACT(EPOCH FROM NOW()) * 1000,
        current_project_id,
        current_tenant_id
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        url = EXCLUDED.url,
        size = EXCLUDED.size;

    -- 插入监控台背景图片资产
    INSERT INTO zutai_assets (
        id,
        name,
        type,
        url,
        size,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        monitor_desk_asset_id,
        'monitor-desk.jpg',
        'image',
        '/assets/images/backgrounds/monitor-desk.jpg',
        17609,
        EXTRACT(EPOCH FROM NOW()) * 1000,
        current_project_id,
        current_tenant_id
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        url = EXCLUDED.url,
        size = EXCLUDED.size;

    -- 输出资产ID信息
    RAISE NOTICE '=== 背景图片资产创建完成 ===';
    RAISE NOTICE '主控制室背景图片资产ID: %', control_room_asset_id;
    RAISE NOTICE '控制面板背景图片资产ID: %', control_panel_asset_id;
    RAISE NOTICE '监控台背景图片资产ID: %', monitor_desk_asset_id;
    RAISE NOTICE '项目ID: %', current_project_id;
    RAISE NOTICE '租户ID: %', current_tenant_id;
    RAISE NOTICE '请复制这些资产ID用于组态配置';

END $$;

-- 查询创建的资产信息
SELECT
    '=== 背景图片资产列表 ===' as 说明,
    id as 资产ID,
    name as 文件名,
    type as 类型,
    url as 路径,
    size as 大小_字节,
    to_timestamp(create_time/1000) as 创建时间,
    project_id as 项目ID,
    tenant_id as 租户ID
FROM zutai_assets
WHERE name LIKE '%control%' OR name LIKE '%monitor%'
ORDER BY create_time DESC;

-- 显示使用说明
SELECT
    '=== 使用说明 ===' as 标题,
    '1. 复制上面查询结果中的资产ID' as 步骤1,
    '2. 执行"创建资产ID工艺总览组态.sql"' as 步骤2,
    '3. 在组态JSON中替换imageId为实际的资产ID' as 步骤3,
    '4. 预览组态查看背景图片效果' as 步骤4;

-- 创建使用资产ID的工艺总览组态模板
-- 注意：需要手动替换下面的资产ID为实际生成的ID

/*
使用方法：
1. 执行上面的SQL脚本
2. 从查询结果中复制实际的资产ID
3. 在下面的组态JSON中替换对应的imageId
4. 执行组态创建脚本

示例资产ID替换：
- "imageId": "实际的control_room_asset_id"
- "imageId": "实际的control_panel_asset_id"
- "imageId": "实际的monitor_desk_asset_id"
*/

-- 创建资产ID工艺总览组态（模板）
INSERT INTO zutai_dashboard_list (
    id,
    dashboard_id,
    name,
    detail,
    protect,
    protect_pwd,
    checked,
    type,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'process_overview_assets_list',
    'process_overview_assets',
    '资产ID工艺总览',
    '使用系统资产ID的专业水厂工艺流程总览监控界面',
    false,
    '',
    true,
    'cloud',
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    detail = EXCLUDED.detail;

-- 注意：下面的JSON需要手动替换imageId为实际生成的资产ID
INSERT INTO zutai_dashboard (
    id,
    data,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'process_overview_assets',
    $JSON${
  "pageConfig": {
    "width": 1920,
    "height": 1080,
    "backgroundColor": "#0a1428",
    "title": "瓜州水厂资产ID工艺总览"
  },
  "assets": [
    {
      "id": "请替换为实际的control_room_asset_id",
      "size": 338530,
      "type": "image",
      "createTime": 1676289207840,
      "projectId": "",
      "tenantId": ""
    },
    {
      "id": "请替换为实际的control_panel_asset_id",
      "size": 22079,
      "type": "image",
      "createTime": 1676289207841,
      "projectId": "",
      "tenantId": ""
    },
    {
      "id": "请替换为实际的monitor_desk_asset_id",
      "size": 17609,
      "type": "image",
      "createTime": 1676289207842,
      "projectId": "",
      "tenantId": ""
    }
  ],
  "id": "process_overview_assets",
  "panels": [
    {
      "visible": true,
      "lock": false,
      "name": "主背景图片",
      "type": "buildin/panel/simple-picture",
      "id": "main_background",
      "config": {
        "style": {
          "height": 1080,
          "width": 1920,
          "x": 0,
          "y": 0,
          "zIndex": 1,
          "opacity": 0.7
        },
        "data": {
          "imageId": "请替换为实际的control_room_asset_id"
        },
        "event": {}
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "系统标题",
      "type": "buildin/panel/text",
      "id": "main_title",
      "config": {
        "style": {
          "height": 80,
          "width": 500,
          "x": 710,
          "y": 20,
          "background": "linear-gradient(135deg, rgba(30, 136, 229, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%)",
          "color": "#ffffff",
          "fontSize": "24px",
          "textAlign": "center",
          "lineHeight": "80px",
          "fontWeight": "bold",
          "borderRadius": "15px",
          "border": "3px solid rgba(66, 165, 245, 0.8)",
          "boxShadow": "0 10px 30px rgba(30, 136, 229, 0.4)",
          "textShadow": "0 2px 4px rgba(0,0,0,0.7)",
          "backdropFilter": "blur(10px)",
          "zIndex": 10
        },
        "data": {
          "text": "🏭 瓜州水厂资产ID工艺总览"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "控制面板装饰",
      "type": "buildin/panel/simple-picture",
      "id": "control_panel_decoration",
      "config": {
        "style": {
          "height": 200,
          "width": 300,
          "x": 50,
          "y": 120,
          "zIndex": 5,
          "borderRadius": "15px",
          "border": "3px solid rgba(66, 165, 245, 0.6)",
          "boxShadow": "0 8px 24px rgba(0,0,0,0.5)",
          "opacity": 0.8
        },
        "data": {
          "imageId": "请替换为实际的control_panel_asset_id"
        },
        "event": {}
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "监控台装饰",
      "type": "buildin/panel/simple-picture",
      "id": "monitor_desk_decoration",
      "config": {
        "style": {
          "height": 150,
          "width": 250,
          "x": 1570,
          "y": 120,
          "zIndex": 5,
          "borderRadius": "12px",
          "border": "2px solid rgba(66, 165, 245, 0.5)",
          "boxShadow": "0 6px 18px rgba(0,0,0,0.4)",
          "opacity": 0.7
        },
        "data": {
          "imageId": "请替换为实际的monitor_desk_asset_id"
        },
        "event": {}
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "说明文本",
      "type": "buildin/panel/text",
      "id": "description_text",
      "config": {
        "style": {
          "height": 120,
          "width": 600,
          "x": 400,
          "y": 400,
          "background": "rgba(55, 71, 79, 0.9)",
          "color": "#e8f5e8",
          "fontSize": "16px",
          "padding": "20px",
          "borderRadius": "10px",
          "border": "2px solid #546e7a",
          "zIndex": 10,
          "backdropFilter": "blur(10px)"
        },
        "data": {
          "text": "🎨 资产ID工艺总览\n\n✅ 使用系统资产管理的背景图片\n✅ 图片通过资产ID引用，加载稳定\n✅ 支持图片缓存和版本管理\n\n📋 如果看到背景图片，说明资产ID配置成功！"
        }
      }
    }
  ],
  "version": "1.0.0",
  "author": "资产管理工程师",
  "lastModified": "2024-01-16T02:00:00Z",
  "description": "使用系统资产ID的专业水厂工艺流程总览监控界面"
}$JSON$,
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
) ON CONFLICT (id) DO UPDATE SET
    data = EXCLUDED.data;

-- 显示完成信息
SELECT
    '=== 资产ID工艺总览创建完成 ===' as 状态,
    '请查看上面的资产ID，并手动替换组态JSON中的imageId' as 说明;
