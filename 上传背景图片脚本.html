<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景图片上传工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #409EFF;
            background: #f0f9ff;
        }
        .upload-area.dragover {
            border-color: #409EFF;
            background: #e6f7ff;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: #409EFF;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .upload-btn:hover {
            background: #337ecc;
        }
        .result-area {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .asset-id {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .preview-img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #f56c6c;
            background: #fef0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #67c23a;
            background: #f0f9ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .instructions {
            background: #fff7e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 背景图片上传工具</h1>
        
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>点击上传区域或拖拽图片文件到上传区域</li>
                <li>支持 JPG、PNG 格式的图片</li>
                <li>上传成功后会显示资产ID</li>
                <li>复制资产ID用于组态配置</li>
            </ol>
        </div>

        <div class="upload-area" id="uploadArea">
            <div>
                <h3>📁 点击或拖拽图片到此处上传</h3>
                <p>支持 JPG、PNG 格式</p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
            </div>
            <input type="file" id="fileInput" class="file-input" accept=".jpg,.jpeg,.png" multiple>
        </div>

        <div class="result-area" id="resultArea">
            <h3>📊 上传结果：</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // 获取用户token
        function getAuthToken() {
            // 从localStorage或sessionStorage获取token
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (!token) {
                alert('请先登录系统！');
                return null;
            }
            return token;
        }

        // 上传文件到系统
        async function uploadFile(file) {
            const token = getAuthToken();
            if (!token) return null;

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/upload/image', {
                    method: 'POST',
                    headers: {
                        'X-Authorization': 'Bearer ' + token
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const imageUrl = await response.text();
                
                // 创建资产记录
                const assetResponse = await fetch('/api/zutai/assets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Authorization': 'Bearer ' + token
                    },
                    body: JSON.stringify({
                        type: 'image',
                        url: imageUrl,
                        name: file.name,
                        size: file.size
                    })
                });

                if (!assetResponse.ok) {
                    throw new Error(`Asset creation failed! status: ${assetResponse.status}`);
                }

                const assetData = await assetResponse.json();
                return {
                    success: true,
                    assetId: assetData.id,
                    imageUrl: imageUrl,
                    fileName: file.name
                };

            } catch (error) {
                console.error('Upload failed:', error);
                return {
                    success: false,
                    error: error.message,
                    fileName: file.name
                };
            }
        }

        // 显示上传结果
        function showResult(result) {
            const resultsDiv = document.getElementById('results');
            const resultArea = document.getElementById('resultArea');
            
            if (result.success) {
                resultsDiv.innerHTML += `
                    <div class="success">
                        <h4>✅ ${result.fileName} 上传成功！</h4>
                        <div class="asset-id">
                            <strong>资产ID：</strong>${result.assetId}
                        </div>
                        <div>
                            <strong>图片URL：</strong>${result.imageUrl}
                        </div>
                        <img src="${result.imageUrl}" class="preview-img" alt="预览">
                        <div style="margin-top: 10px;">
                            <button class="upload-btn" onclick="copyToClipboard('${result.assetId}')">
                                复制资产ID
                            </button>
                        </div>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML += `
                    <div class="error">
                        <h4>❌ ${result.fileName} 上传失败！</h4>
                        <p>错误信息：${result.error}</p>
                    </div>
                `;
            }
            
            resultArea.style.display = 'block';
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('资产ID已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 处理文件上传
        async function handleFiles(files) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = ''; // 清空之前的结果

            for (let file of files) {
                if (!file.type.startsWith('image/')) {
                    showResult({
                        success: false,
                        error: '只支持图片文件',
                        fileName: file.name
                    });
                    continue;
                }

                showResult({
                    success: false,
                    error: '正在上传...',
                    fileName: file.name
                });

                const result = await uploadFile(file);
                showResult(result);
            }
        }

        // 设置事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        });
    </script>
</body>
</html>
