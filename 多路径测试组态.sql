-- 多路径测试组态配置
-- 测试不同的图片路径格式

-- 清理旧数据
DELETE FROM zutai_dashboard WHERE id = 'multi_path_test';
DELETE FROM zutai_dashboard_list WHERE id = 'multi_path_test_list';

-- 插入多路径测试组态列表
INSERT INTO zutai_dashboard_list (
    id,
    dashboard_id,
    name,
    detail,
    protect,
    protect_pwd,
    checked,
    type,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'multi_path_test_list',
    'multi_path_test',
    '多路径测试',
    '测试不同图片路径格式的组态',
    false,
    '',
    true,
    'cloud',
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
);

-- 插入多路径测试组态内容
INSERT INTO zutai_dashboard (
    id,
    data,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'multi_path_test',
    $JSON${
  "pageConfig": {
    "width": 1920,
    "height": 1080,
    "backgroundColor": "#0a1428",
    "title": "多路径测试"
  },
  "id": "multi_path_test",
  "panels": [
    {
      "visible": true,
      "lock": false,
      "name": "路径1: /assets/",
      "type": "buildin/panel/text",
      "id": "path_test_1",
      "config": {
        "style": {
          "height": 150,
          "width": 300,
          "x": 50,
          "y": 50,
          "backgroundImage": "url('/assets/images/backgrounds/control-room-main.jpg')",
          "backgroundSize": "cover",
          "backgroundPosition": "center",
          "border": "2px solid #42a5f5",
          "borderRadius": "10px"
        },
        "data": {
          "text": ""
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径1说明",
      "type": "buildin/panel/text",
      "id": "path_desc_1",
      "config": {
        "style": {
          "height": 30,
          "width": 300,
          "x": 50,
          "y": 210,
          "background": "rgba(0,0,0,0.7)",
          "color": "#ffffff",
          "fontSize": "12px",
          "textAlign": "center",
          "lineHeight": "30px"
        },
        "data": {
          "text": "路径1: /assets/images/backgrounds/"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径2: ./assets/",
      "type": "buildin/panel/text",
      "id": "path_test_2",
      "config": {
        "style": {
          "height": 150,
          "width": 300,
          "x": 400,
          "y": 50,
          "backgroundImage": "url('./assets/images/backgrounds/control-room-main.jpg')",
          "backgroundSize": "cover",
          "backgroundPosition": "center",
          "border": "2px solid #42a5f5",
          "borderRadius": "10px"
        },
        "data": {
          "text": ""
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径2说明",
      "type": "buildin/panel/text",
      "id": "path_desc_2",
      "config": {
        "style": {
          "height": 30,
          "width": 300,
          "x": 400,
          "y": 210,
          "background": "rgba(0,0,0,0.7)",
          "color": "#ffffff",
          "fontSize": "12px",
          "textAlign": "center",
          "lineHeight": "30px"
        },
        "data": {
          "text": "路径2: ./assets/images/backgrounds/"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径3: static/",
      "type": "buildin/panel/text",
      "id": "path_test_3",
      "config": {
        "style": {
          "height": 150,
          "width": 300,
          "x": 750,
          "y": 50,
          "backgroundImage": "url('/static/assets/images/backgrounds/control-room-main.jpg')",
          "backgroundSize": "cover",
          "backgroundPosition": "center",
          "border": "2px solid #42a5f5",
          "borderRadius": "10px"
        },
        "data": {
          "text": ""
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径3说明",
      "type": "buildin/panel/text",
      "id": "path_desc_3",
      "config": {
        "style": {
          "height": 30,
          "width": 300,
          "x": 750,
          "y": 210,
          "background": "rgba(0,0,0,0.7)",
          "color": "#ffffff",
          "fontSize": "12px",
          "textAlign": "center",
          "lineHeight": "30px"
        },
        "data": {
          "text": "路径3: /static/assets/images/backgrounds/"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径4: 完整URL",
      "type": "buildin/panel/text",
      "id": "path_test_4",
      "config": {
        "style": {
          "height": 150,
          "width": 300,
          "x": 1100,
          "y": 50,
          "backgroundImage": "url('http://localhost:8081/assets/images/backgrounds/control-room-main.jpg')",
          "backgroundSize": "cover",
          "backgroundPosition": "center",
          "border": "2px solid #42a5f5",
          "borderRadius": "10px"
        },
        "data": {
          "text": ""
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径4说明",
      "type": "buildin/panel/text",
      "id": "path_desc_4",
      "config": {
        "style": {
          "height": 30,
          "width": 300,
          "x": 1100,
          "y": 210,
          "background": "rgba(0,0,0,0.7)",
          "color": "#ffffff",
          "fontSize": "12px",
          "textAlign": "center",
          "lineHeight": "30px"
        },
        "data": {
          "text": "路径4: http://localhost:8081/assets/"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径5: 在线图片",
      "type": "buildin/panel/text",
      "id": "path_test_5",
      "config": {
        "style": {
          "height": 150,
          "width": 300,
          "x": 50,
          "y": 300,
          "backgroundImage": "url('https://images.unsplash.com/photo-1685720543547-cc4873188c75?w=300&h=150&fit=crop&crop=center')",
          "backgroundSize": "cover",
          "backgroundPosition": "center",
          "border": "2px solid #4caf50",
          "borderRadius": "10px"
        },
        "data": {
          "text": ""
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "路径5说明",
      "type": "buildin/panel/text",
      "id": "path_desc_5",
      "config": {
        "style": {
          "height": 30,
          "width": 300,
          "x": 50,
          "y": 460,
          "background": "rgba(0,0,0,0.7)",
          "color": "#4caf50",
          "fontSize": "12px",
          "textAlign": "center",
          "lineHeight": "30px"
        },
        "data": {
          "text": "路径5: 在线图片 (参考对比)"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "测试说明",
      "type": "buildin/panel/text",
      "id": "test_description",
      "config": {
        "style": {
          "height": 200,
          "width": 800,
          "x": 400,
          "y": 300,
          "background": "rgba(55, 71, 79, 0.9)",
          "color": "#e8f5e8",
          "fontSize": "14px",
          "padding": "20px",
          "borderRadius": "10px",
          "border": "1px solid #546e7a"
        },
        "data": {
          "text": "📋 路径测试说明：\n\n✅ 如果某个区域显示了工业控制室图片，说明该路径格式正确\n❌ 如果某个区域是空白的，说明该路径无法访问\n\n🎯 测试路径：\n1. /assets/ - Spring Boot默认静态资源路径\n2. ./assets/ - 相对路径\n3. /static/ - 可能的静态资源映射路径\n4. 完整URL - 绝对URL路径\n5. 在线图片 - 网络图片作为对比\n\n📝 请观察哪个路径能正常显示图片，然后使用该路径格式"
        }
      }
    }
  ],
  "version": "1.0.0",
  "author": "路径测试工程师",
  "lastModified": "2024-01-16T01:00:00Z",
  "description": "测试不同图片路径格式的组态，找出正确的路径配置"
}$JSON$,
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
);

-- 验证插入结果
SELECT 
    '多路径测试组态创建完成' as 状态,
    dl.name as 组态名称,
    LENGTH(d.data) as JSON大小,
    '字节' as 单位
FROM zutai_dashboard_list dl
LEFT JOIN zutai_dashboard d ON dl.dashboard_id = d.id
WHERE dl.id = 'multi_path_test_list';
