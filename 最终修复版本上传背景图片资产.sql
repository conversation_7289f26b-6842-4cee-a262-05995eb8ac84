-- 最终修复版本：上传背景图片资产到系统
-- 使用正确的Data URL格式：data:image/jpeg;base64,xxxxx

-- 清理旧的背景图片资产
DELETE FROM zutai_assets WHERE id LIKE 'bg_final_%';

-- 获取当前项目和租户信息并插入资产
DO $$
DECLARE
    current_project_id TEXT;
    current_tenant_id TEXT;
    final_bg_asset_id TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list 
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL 
    LIMIT 1;
    
    -- 如果没有找到，使用默认值
    IF current_project_id IS NULL THEN
        current_project_id := 'guazhou_project';
    END IF;
    
    IF current_tenant_id IS NULL THEN
        current_tenant_id := 'guazhou_tenant';
    END IF;
    
    -- 生成唯一的资产ID
    final_bg_asset_id := 'bg_final_dataurl_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    
    -- 插入背景图片资产（使用完整的Data URL格式）
    INSERT INTO zutai_assets (
        id,
        uri,
        size,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        final_bg_asset_id,
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
        '68',
        'image',
        NOW(),
        current_project_id,
        current_tenant_id
    );
    
    -- 输出资产ID信息
    RAISE NOTICE '=== 最终修复版本背景图片资产创建完成 ===';
    RAISE NOTICE '背景图片资产ID: %', final_bg_asset_id;
    RAISE NOTICE '使用Data URL格式: data:image/png;base64,xxxxx';
    RAISE NOTICE '项目ID: %', current_project_id;
    RAISE NOTICE '租户ID: %', current_tenant_id;
    RAISE NOTICE '请复制这个资产ID用于组态配置';
    
END $$;

-- 查询创建的资产信息
SELECT 
    '=== 最终修复版本背景图片资产列表 ===' as 说明,
    id as 资产ID,
    LEFT(uri, 50) || '...' as URI前50字符,
    size as 大小_字节,
    type as 类型,
    create_time as 创建时间,
    project_id as 项目ID,
    tenant_id as 租户ID
FROM zutai_assets 
WHERE id LIKE 'bg_final_%'
ORDER BY create_time DESC;

-- 显示使用说明
SELECT 
    '=== 使用说明 ===' as 标题,
    '1. 复制上面查询结果中的资产ID' as 步骤1,
    '2. 执行"最终修复版本创建组态.sql"' as 步骤2,
    '3. 如果能正常显示，说明Data URL格式正确' as 步骤3,
    '4. 然后可以替换为真实图片的Data URL' as 步骤4;
