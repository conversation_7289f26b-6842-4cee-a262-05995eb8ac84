-- 水厂工艺总览组态创建
-- 注意：执行前请先运行"水厂工艺总览组态资产.sql"获取资产ID

-- 清理旧的组态
DELETE FROM zutai_dashboard WHERE id = 'water_plant_process_overview';
DELETE FROM zutai_dashboard_list WHERE id = 'water_plant_process_overview_list';

-- 获取最新的背景图片资产ID并创建组态
DO $$
DECLARE
    main_bg_id TEXT;
    control_panel_id TEXT;
    monitor_desk_id TEXT;
    current_project_id TEXT;
    current_tenant_id TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL
    LIMIT 1;

    -- 获取背景图片资产ID
    SELECT id INTO main_bg_id FROM zutai_assets WHERE id LIKE 'wp_main_%' ORDER BY create_time DESC LIMIT 1;
    SELECT id INTO control_panel_id FROM zutai_assets WHERE id LIKE 'wp_panel_%' ORDER BY create_time DESC LIMIT 1;
    SELECT id INTO monitor_desk_id FROM zutai_assets WHERE id LIKE 'wp_desk_%' ORDER BY create_time DESC LIMIT 1;

    -- 检查是否找到了资产ID
    IF main_bg_id IS NULL OR control_panel_id IS NULL OR monitor_desk_id IS NULL THEN
        RAISE EXCEPTION '未找到背景图片资产，请先执行"水厂工艺总览组态资产.sql"';
    END IF;

    -- 输出找到的资产ID
    RAISE NOTICE '=== 使用的资产ID ===';
    RAISE NOTICE '主控制室背景: %', main_bg_id;
    RAISE NOTICE '控制面板: %', control_panel_id;
    RAISE NOTICE '监控台: %', monitor_desk_id;

    -- 创建组态列表记录
    INSERT INTO zutai_dashboard_list (
        id,
        dashboard_id,
        name,
        detail,
        protect,
        protect_pwd,
        checked,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'water_plant_process_overview_list',
        'water_plant_process_overview',
        '瓜州水厂工艺总览',
        '专业的水厂工艺流程总览监控界面，包含完整的水处理工艺流程',
        false,
        '',
        true,
        'cloud',
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );

    -- 创建组态内容
    INSERT INTO zutai_dashboard (
        id,
        data,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'water_plant_process_overview',
        jsonb_build_object(
            'pageConfig', jsonb_build_object(
                'width', 1920,
                'height', 1080,
                'backgroundColor', '#0a1428',
                'title', '瓜州水厂工艺总览'
            ),
            'assets', jsonb_build_array(
                jsonb_build_object(
                    'id', main_bg_id,
                    'size', 338530,
                    'type', 'image',
                    'createTime', EXTRACT(EPOCH FROM NOW()) * 1000,
                    'projectId', COALESCE(current_project_id, ''),
                    'tenantId', COALESCE(current_tenant_id, '')
                ),
                jsonb_build_object(
                    'id', control_panel_id,
                    'size', 22079,
                    'type', 'image',
                    'createTime', EXTRACT(EPOCH FROM NOW()) * 1000,
                    'projectId', COALESCE(current_project_id, ''),
                    'tenantId', COALESCE(current_tenant_id, '')
                ),
                jsonb_build_object(
                    'id', monitor_desk_id,
                    'size', 17609,
                    'type', 'image',
                    'createTime', EXTRACT(EPOCH FROM NOW()) * 1000,
                    'projectId', COALESCE(current_project_id, ''),
                    'tenantId', COALESCE(current_tenant_id, '')
                )
            ),
            'id', 'water_plant_process_overview',
            'panels', jsonb_build_array(
                -- 主背景图片
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '主控制室背景',
                    'type', 'buildin/panel/simple-picture',
                    'id', 'main_background',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 1080,
                            'width', 1920,
                            'x', 0,
                            'y', 0,
                            'zIndex', 1,
                            'opacity', 0.6
                        ),
                        'data', jsonb_build_object(
                            'imageId', main_bg_id
                        ),
                        'event', jsonb_build_object()
                    )
                ),
                -- 系统标题
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '系统标题',
                    'type', 'buildin/panel/text',
                    'id', 'main_title',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 80,
                            'width', 600,
                            'x', 660,
                            'y', 20,
                            'background', 'linear-gradient(135deg, rgba(30, 136, 229, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '28px',
                            'textAlign', 'center',
                            'lineHeight', '80px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(66, 165, 245, 0.8)',
                            'boxShadow', '0 10px 30px rgba(30, 136, 229, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 20
                        ),
                        'data', jsonb_build_object(
                            'text', '🏭 瓜州水厂工艺总览'
                        )
                    )
                ),
                -- 原水取水工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '原水取水',
                    'type', 'buildin/panel/text',
                    'id', 'raw_water_intake',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 100,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(66, 165, 245, 0.95) 0%, rgba(25, 118, 210, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(100, 181, 246, 0.8)',
                            'boxShadow', '0 8px 20px rgba(66, 165, 245, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '💧 原水取水\n\n流量: 1200m³/h\n水位: 3.2m\n状态: 正常'
                        )
                    )
                ),
                -- 预处理工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '预处理',
                    'type', 'buildin/panel/text',
                    'id', 'pretreatment',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 320,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(102, 187, 106, 0.95) 0%, rgba(56, 142, 60, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(129, 199, 132, 0.8)',
                            'boxShadow', '0 8px 20px rgba(102, 187, 106, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🔧 预处理\n\n格栅: 运行\n沉砂: 正常\n效率: 85%'
                        )
                    )
                ),
                -- 混凝沉淀工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '混凝沉淀',
                    'type', 'buildin/panel/text',
                    'id', 'coagulation',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 540,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(255, 152, 0, 0.95) 0%, rgba(245, 124, 0, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(255, 183, 77, 0.8)',
                            'boxShadow', '0 8px 20px rgba(255, 152, 0, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🌀 混凝沉淀\n\n浊度: 2.1 NTU\n去除率: 75%\nPAC: 15mg/L'
                        )
                    )
                ),
                -- 过滤工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '过滤',
                    'type', 'buildin/panel/text',
                    'id', 'filtration',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 760,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(171, 71, 188, 0.95) 0%, rgba(123, 31, 162, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(186, 104, 200, 0.8)',
                            'boxShadow', '0 8px 20px rgba(171, 71, 188, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🔍 过滤\n\n滤池: 4座运行\n浊度: 0.3 NTU\n反冲洗: 正常'
                        )
                    )
                ),
                -- 消毒工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '消毒',
                    'type', 'buildin/panel/text',
                    'id', 'disinfection',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 980,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(244, 67, 54, 0.95) 0%, rgba(211, 47, 47, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(239, 83, 80, 0.8)',
                            'boxShadow', '0 8px 20px rgba(244, 67, 54, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🧪 消毒\n\n余氯: 0.8mg/L\n接触时间: 30min\n二氧化氯: 正常'
                        )
                    )
                ),
                -- 清水池工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '清水池',
                    'type', 'buildin/panel/text',
                    'id', 'clear_water',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 1200,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(38, 198, 218, 0.95) 0%, rgba(0, 131, 143, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(77, 208, 225, 0.8)',
                            'boxShadow', '0 8px 20px rgba(38, 198, 218, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🏊 清水池\n\n水位: 85%\n容量: 5000m³\n水质: 优良'
                        )
                    )
                ),
                -- 送水泵站工艺段
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '送水泵站',
                    'type', 'buildin/panel/text',
                    'id', 'distribution',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 1420,
                            'y', 150,
                            'background', 'linear-gradient(135deg, rgba(76, 175, 80, 0.95) 0%, rgba(46, 125, 50, 0.95) 100%)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'textAlign', 'center',
                            'lineHeight', '30px',
                            'fontWeight', 'bold',
                            'borderRadius', '15px',
                            'border', '3px solid rgba(102, 187, 106, 0.8)',
                            'boxShadow', '0 8px 20px rgba(76, 175, 80, 0.4)',
                            'textShadow', '0 2px 4px rgba(0,0,0,0.7)',
                            'backdropFilter', 'blur(10px)',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🚰 送水泵站\n\n出厂: 1150m³/h\n压力: 0.45MPa\n泵组: 3台运行'
                        )
                    )
                ),
                -- 控制面板装饰
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '控制面板装饰',
                    'type', 'buildin/panel/simple-picture',
                    'id', 'control_panel_decoration',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 150,
                            'width', 200,
                            'x', 100,
                            'y', 320,
                            'zIndex', 5,
                            'borderRadius', '15px',
                            'border', '2px solid rgba(66, 165, 245, 0.6)',
                            'boxShadow', '0 8px 24px rgba(0,0,0,0.5)',
                            'opacity', 0.8
                        ),
                        'data', jsonb_build_object(
                            'imageId', control_panel_id
                        ),
                        'event', jsonb_build_object()
                    )
                ),
                -- 监控台装饰
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '监控台装饰',
                    'type', 'buildin/panel/simple-picture',
                    'id', 'monitor_desk_decoration',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 120,
                            'width', 180,
                            'x', 1420,
                            'y', 320,
                            'zIndex', 5,
                            'borderRadius', '12px',
                            'border', '2px solid rgba(66, 165, 245, 0.5)',
                            'boxShadow', '0 6px 18px rgba(0,0,0,0.4)',
                            'opacity', 0.7
                        ),
                        'data', jsonb_build_object(
                            'imageId', monitor_desk_id
                        ),
                        'event', jsonb_build_object()
                    )
                ),
                -- 工艺流程连接线
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '工艺流程指示',
                    'type', 'buildin/panel/text',
                    'id', 'process_flow_indicator',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 60,
                            'width', 1400,
                            'x', 100,
                            'y', 290,
                            'background', 'rgba(30, 136, 229, 0.1)',
                            'color', '#42a5f5',
                            'fontSize', '24px',
                            'textAlign', 'center',
                            'lineHeight', '60px',
                            'fontWeight', 'bold',
                            'borderRadius', '30px',
                            'border', '2px solid rgba(66, 165, 245, 0.3)',
                            'zIndex', 2
                        ),
                        'data', jsonb_build_object(
                            'text', '→ → → → → → →'
                        )
                    )
                ),
                -- 系统状态信息
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '系统状态信息',
                    'type', 'buildin/panel/text',
                    'id', 'system_status',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 200,
                            'width', 400,
                            'x', 760,
                            'y', 500,
                            'background', 'rgba(55, 71, 79, 0.9)',
                            'color', '#e8f5e8',
                            'fontSize', '14px',
                            'padding', '20px',
                            'borderRadius', '15px',
                            'border', '2px solid #546e7a',
                            'zIndex', 10,
                            'backdropFilter', 'blur(10px)'
                        ),
                        'data', jsonb_build_object(
                            'text', '📊 瓜州水厂实时状态\n\n🔹 总处理能力: 50000m³/d\n🔹 当前处理量: 1150m³/h\n🔹 设备运行率: 98.5%\n🔹 出水水质: 优良\n🔹 能耗指标: 0.35kWh/m³\n🔹 系统状态: 正常运行\n\n⏰ 更新时间: ' || TO_CHAR(NOW(), 'YYYY-MM-DD HH24:MI:SS')
                        )
                    )
                )
            ),
            'version', '1.0.0',
            'author', '水厂工艺工程师',
            'lastModified', '2024-01-16T06:00:00Z',
            'description', '专业的水厂工艺流程总览监控界面，包含完整的水处理工艺流程'
        ),
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );

    RAISE NOTICE '=== 瓜州水厂工艺总览组态创建完成 ===';
    RAISE NOTICE '请刷新组态管理页面，查找"瓜州水厂工艺总览"并预览';

END $$;

-- 验证创建结果
SELECT
    '=== 创建结果验证 ===' as 状态,
    dl.name as 组态名称,
    LENGTH(d.data::text) as JSON大小,
    '字节' as 单位,
    d.create_time as 创建时间
FROM zutai_dashboard_list dl
LEFT JOIN zutai_dashboard d ON dl.dashboard_id = d.id
WHERE dl.id = 'water_plant_process_overview_list';
