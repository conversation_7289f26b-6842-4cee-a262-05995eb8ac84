-- 正确的上传背景图片资产到系统
-- 根据实际的AssetsEntity实体类和数据库表结构创建

-- 清理旧的背景图片资产
DELETE FROM zutai_assets WHERE id LIKE 'bg_control_room_%' OR id LIKE 'bg_control_panel_%' OR id LIKE 'bg_monitor_desk_%';

-- 获取当前项目和租户信息并插入资产
DO $$
DECLARE
    current_project_id TEXT;
    current_tenant_id TEXT;
    control_room_asset_id TEXT;
    control_panel_asset_id TEXT;
    monitor_desk_asset_id TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list 
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL 
    LIMIT 1;
    
    -- 如果没有找到，使用默认值
    IF current_project_id IS NULL THEN
        current_project_id := 'guazhou_project';
    END IF;
    
    IF current_tenant_id IS NULL THEN
        current_tenant_id := 'guazhou_tenant';
    END IF;
    
    -- 生成唯一的资产ID
    control_room_asset_id := 'bg_control_room_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    control_panel_asset_id := 'bg_control_panel_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    monitor_desk_asset_id := 'bg_monitor_desk_' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    
    -- 插入主控制室背景图片资产（使用正确的字段名）
    INSERT INTO zutai_assets (
        id,
        uri,
        size,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        control_room_asset_id,
        '+s93QwK6X9HVZfz0GSD1cKvFYbRfmqyD1cKyCvBx/JQekfIH8lCgR8gfyUKAUPe0KFB6b8mP5cKt4du10qroUFuqjfXqqvTfkx/LhQf/Z',
        '338530',
        'image',
        NOW(),
        current_project_id,
        current_tenant_id
    );
    
    -- 插入控制面板背景图片资产
    INSERT INTO zutai_assets (
        id,
        uri,
        size,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        control_panel_asset_id,
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
        '22079',
        'image',
        NOW(),
        current_project_id,
        current_tenant_id
    );
    
    -- 插入监控台背景图片资产
    INSERT INTO zutai_assets (
        id,
        uri,
        size,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        monitor_desk_asset_id,
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
        '17609',
        'image',
        NOW(),
        current_project_id,
        current_tenant_id
    );
    
    -- 输出资产ID信息
    RAISE NOTICE '=== 背景图片资产创建完成 ===';
    RAISE NOTICE '主控制室背景图片资产ID: %', control_room_asset_id;
    RAISE NOTICE '控制面板背景图片资产ID: %', control_panel_asset_id;
    RAISE NOTICE '监控台背景图片资产ID: %', monitor_desk_asset_id;
    RAISE NOTICE '项目ID: %', current_project_id;
    RAISE NOTICE '租户ID: %', current_tenant_id;
    RAISE NOTICE '请复制这些资产ID用于组态配置';
    
END $$;

-- 查询创建的资产信息
SELECT 
    '=== 背景图片资产列表 ===' as 说明,
    id as 资产ID,
    LEFT(uri, 50) || '...' as URI前50字符,
    size as 大小_字节,
    type as 类型,
    create_time as 创建时间,
    project_id as 项目ID,
    tenant_id as 租户ID
FROM zutai_assets 
WHERE id LIKE 'bg_%'
ORDER BY create_time DESC;

-- 显示使用说明
SELECT 
    '=== 使用说明 ===' as 标题,
    '1. 复制上面查询结果中的资产ID' as 步骤1,
    '2. 执行"正确的创建资产ID工艺总览组态.sql"' as 步骤2,
    '3. 组态会自动使用这些资产ID' as 步骤3,
    '4. 预览组态查看背景图片效果' as 步骤4;
