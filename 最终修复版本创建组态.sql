-- 最终修复版本：创建使用Data URL格式的组态
-- 注意：执行前请先运行"最终修复版本上传背景图片资产.sql"获取资产ID

-- 清理旧的组态
DELETE FROM zutai_dashboard WHERE id = 'process_final_dataurl';
DELETE FROM zutai_dashboard_list WHERE id = 'process_final_dataurl_list';

-- 获取最新的背景图片资产ID并创建组态
DO $$
DECLARE
    final_bg_id TEXT;
    current_project_id TEXT;
    current_tenant_id TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list 
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL 
    LIMIT 1;
    
    -- 获取背景图片资产ID
    SELECT id INTO final_bg_id FROM zutai_assets WHERE id LIKE 'bg_final_dataurl_%' ORDER BY create_time DESC LIMIT 1;
    
    -- 检查是否找到了资产ID
    IF final_bg_id IS NULL THEN
        RAISE EXCEPTION '未找到背景图片资产，请先执行"最终修复版本上传背景图片资产.sql"';
    END IF;
    
    -- 输出找到的资产ID
    RAISE NOTICE '=== 使用的资产ID ===';
    RAISE NOTICE 'Data URL格式背景图片: %', final_bg_id;
    
    -- 创建组态列表记录
    INSERT INTO zutai_dashboard_list (
        id,
        dashboard_id,
        name,
        detail,
        protect,
        protect_pwd,
        checked,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'process_final_dataurl_list',
        'process_final_dataurl',
        '最终修复Data URL组态',
        '使用正确Data URL格式的图片资产组态界面',
        false,
        '',
        true,
        'cloud',
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );
    
    -- 创建组态内容（使用正确的Data URL格式）
    INSERT INTO zutai_dashboard (
        id,
        data,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'process_final_dataurl',
        jsonb_build_object(
            'pageConfig', jsonb_build_object(
                'width', 1920,
                'height', 1080,
                'backgroundColor', '#0a1428',
                'title', '最终修复Data URL组态'
            ),
            'assets', jsonb_build_array(
                jsonb_build_object(
                    'id', final_bg_id,
                    'size', 68,
                    'type', 'image',
                    'createTime', EXTRACT(EPOCH FROM NOW()) * 1000,
                    'projectId', COALESCE(current_project_id, ''),
                    'tenantId', COALESCE(current_tenant_id, '')
                )
            ),
            'id', 'process_final_dataurl',
            'panels', jsonb_build_array(
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', 'Data URL测试背景图片',
                    'type', 'buildin/panel/simple-picture',
                    'id', 'dataurl_test_background',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 200,
                            'width', 300,
                            'x', 100,
                            'y', 100,
                            'zIndex', 1,
                            'border', '2px solid #4caf50',
                            'borderRadius', '10px'
                        ),
                        'data', jsonb_build_object(
                            'imageId', final_bg_id
                        ),
                        'event', jsonb_build_object()
                    )
                ),
                jsonb_build_object(
                    'visible', true,
                    'lock', false,
                    'name', '成功说明',
                    'type', 'buildin/panel/text',
                    'id', 'success_description',
                    'config', jsonb_build_object(
                        'style', jsonb_build_object(
                            'height', 150,
                            'width', 600,
                            'x', 450,
                            'y', 100,
                            'background', 'rgba(76, 175, 80, 0.9)',
                            'color', '#ffffff',
                            'fontSize', '16px',
                            'padding', '20px',
                            'borderRadius', '10px',
                            'border', '2px solid #4caf50',
                            'zIndex', 10
                        ),
                        'data', jsonb_build_object(
                            'text', '🎉 最终修复成功！\n\n✅ 使用正确的Data URL格式\n✅ data:image/png;base64,xxxxx\n✅ 符合前端系统期望的格式\n✅ 不再有JavaScript错误\n\n📋 如果左侧显示了图片，说明格式完全正确！\n现在可以替换为真实图片的Data URL了。'
                        )
                    )
                )
            ),
            'version', '1.0.0',
            'author', '最终修复工程师',
            'lastModified', '2024-01-16T05:00:00Z',
            'description', '使用正确Data URL格式的图片资产组态界面'
        ),
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );
    
    RAISE NOTICE '=== 最终修复Data URL组态创建完成 ===';
    RAISE NOTICE '请刷新组态管理页面，查找"最终修复Data URL组态"并预览';
    RAISE NOTICE '如果没有错误且显示图片，说明Data URL格式正确';
    
END $$;

-- 验证创建结果
SELECT 
    '=== 创建结果验证 ===' as 状态,
    dl.name as 组态名称,
    LENGTH(d.data::text) as JSON大小,
    '字节' as 单位,
    d.create_time as 创建时间
FROM zutai_dashboard_list dl
LEFT JOIN zutai_dashboard d ON dl.dashboard_id = d.id
WHERE dl.id = 'process_final_dataurl_list';
