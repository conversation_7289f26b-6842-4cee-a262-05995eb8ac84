-- 前端资源工艺总览组态配置
-- 使用前端打包后的静态资源路径

-- 清理旧数据
DELETE FROM zutai_dashboard WHERE id = 'process_overview_frontend';
DELETE FROM zutai_dashboard_list WHERE id = 'process_overview_frontend_list';

-- 插入前端资源工艺总览组态列表
INSERT INTO zutai_dashboard_list (
    id,
    dashboard_id,
    name,
    detail,
    protect,
    protect_pwd,
    checked,
    type,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'process_overview_frontend_list',
    'process_overview_frontend',
    '前端资源工艺总览',
    '使用前端打包资源的专业水厂工艺流程总览监控界面',
    false,
    '',
    true,
    'cloud',
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
);

-- 插入前端资源工艺总览组态内容
INSERT INTO zutai_dashboard (
    id,
    data,
    create_time,
    project_id,
    tenant_id
) VALUES (
    'process_overview_frontend',
    $JSON${
  "pageConfig": {
    "width": 1920,
    "height": 1080,
    "backgroundColor": "#0a1428",
    "backgroundImage": "url('./src/assets/images/backgrounds/control-room-main.jpg'), radial-gradient(ellipse at center, rgba(26, 35, 50, 0.85) 0%, rgba(15, 20, 25, 0.95) 70%)",
    "backgroundSize": "cover",
    "backgroundPosition": "center",
    "backgroundBlendMode": "overlay",
    "title": "瓜州水厂前端资源工艺总览"
  },
  "id": "process_overview_frontend",
  "panels": [
    {
      "visible": true,
      "lock": false,
      "name": "系统标题",
      "type": "buildin/panel/text",
      "id": "main_title",
      "config": {
        "style": {
          "height": 80,
          "width": 500,
          "x": 710,
          "y": 20,
          "background": "linear-gradient(135deg, rgba(30, 136, 229, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%)",
          "color": "#ffffff",
          "fontSize": "24px",
          "textAlign": "center",
          "lineHeight": "80px",
          "fontWeight": "bold",
          "borderRadius": "15px",
          "border": "3px solid rgba(66, 165, 245, 0.8)",
          "boxShadow": "0 10px 30px rgba(30, 136, 229, 0.4), inset 0 2px 4px rgba(255,255,255,0.2)",
          "textShadow": "0 2px 4px rgba(0,0,0,0.7)",
          "backdropFilter": "blur(10px)"
        },
        "data": {
          "text": "🏭 瓜州水厂前端资源工艺总览"
        }
      }
    },
    {
      "visible": true,
      "lock": false,
      "name": "原水取水",
      "type": "buildin/panel/text",
      "id": "raw_water_intake",
      "config": {
        "style": {
          "height": 120,
          "width": 180,
          "x": 100,
          "y": 150,
          "background": "linear-gradient(135deg, rgba(66, 165, 245, 0.95) 0%, rgba(25, 118, 210, 0.95) 100%)",
          "color": "#ffffff",
          "fontSize": "16px",
          "textAlign": "center",
          "lineHeight": "30px",
          "fontWeight": "bold",
          "borderRadius": "15px",
          "border": "3px solid rgba(100, 181, 246, 0.8)",
          "boxShadow": "0 8px 20px rgba(66, 165, 245, 0.4), inset 0 2px 4px rgba(255,255,255,0.2)",
          "textShadow": "0 2px 4px rgba(0,0,0,0.7)",
          "backdropFilter": "blur(10px)"
        },
        "data": {
          "text": "💧 原水取水\n\n流量: 1200m³/h\n水位: 3.2m"
        }
      }
    }
  ],
  "version": "1.0.0",
  "author": "前端工程师",
  "lastModified": "2024-01-16T00:00:00Z",
  "description": "使用前端打包资源的专业水厂工艺流程总览监控界面"
}$JSON$,
    NOW(),
    COALESCE((SELECT project_id FROM zutai_dashboard_list WHERE project_id IS NOT NULL LIMIT 1), 'guazhou_project'),
    COALESCE((SELECT tenant_id FROM zutai_dashboard_list WHERE tenant_id IS NOT NULL LIMIT 1), 'guazhou_tenant')
);

-- 验证插入结果
SELECT 
    '前端资源工艺总览组态创建完成' as 状态,
    dl.name as 组态名称,
    LENGTH(d.data) as JSON大小,
    '字节' as 单位
FROM zutai_dashboard_list dl
LEFT JOIN zutai_dashboard d ON dl.dashboard_id = d.id
WHERE dl.id = 'process_overview_frontend_list';
